from django.db import models
import uuid
from cls_backend.models import User, TimeStampedModel
from .constants import *


class Organization(TimeStampedModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    # address = models.CharField(max_length=500, blank=True, null=True)
    # phone = models.CharField(max_length=20, blank=True, null=True)
    # email = models.EmailField(blank=True, null=True)
    # website = models.URLField(blank=True, null=True)
    # logo = models.ImageField(upload_to='organization_logos/', blank=True, null=True)
    # is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"


class OrganizationMember(models.Model):
    ROLE_CHOICES = [
        (ADMIN, 'Admin'),
        (MEMBER, 'Member'),
    ]

    id = models.U<PERSON><PERSON>ield(primary_key=True, default=uuid.uuid4, editable=False)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='organization_memberships')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='member')
    is_active = models.BooleanField(default=True)
    joined_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('organization', 'user')
        verbose_name = "Organization Member"
        verbose_name_plural = "Organization Members"

    def __str__(self):
        return f"{self.user.email} - {self.organization.name} ({self.role})"