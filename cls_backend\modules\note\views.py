from cls_backend.models import Note
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from cls_backend.modules.note.serializers import NoteSerializer

class NoteViewSet(viewsets.ModelViewSet):
    queryset = Note.objects.all()
    serializer_class = NoteSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Note.objects.all()
        workspace_id = self.request.query_params.get('workspace_id')
        if workspace_id:
            queryset = queryset.filter(workspace_id=workspace_id)
        return queryset.order_by('-created_at')