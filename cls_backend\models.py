# Create your models here.
import mimetypes
import uuid

import jwt
from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.postgres.fields import ArrayField
from django.contrib.auth.models import PermissionsMixin
from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models

from cls_backend import constants as const, settings
from cls_backend.modules.auth.manager import CustomUserManager
from cls_backend.constants import *


class User(AbstractBaseUser, PermissionsMixin):
    email = models.CharField(unique=True, max_length=255)
    password = models.CharField(max_length=255)
    avatar = models.FileField(upload_to='avatars', null=True, blank=True)
    fullname = models.CharField(max_length=255)
    created_time = models.DateTimeField(auto_now_add=True)
    updated_time = models.DateTimeField(auto_now=True)
    deleted_time = models.DateTimeField(null=True, blank=True)
    verification_code = models.TextField(null=True, blank=True)
    verification_code_created_at = models.DateTimeField(null=True, blank=True)

    is_staff = models.BooleanField(
        default=False,
        help_text='Designates whether the user can log into this admin site.',
    )

    is_active = models.BooleanField(
        default=True,
        help_text='Designates whether this user should be treated as active. ''Unselect this instead of deleting '
                  'accounts.',
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = CustomUserManager()


class TimeStampedModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True

class MetadataDocument(models.Model):
    VALIDITY_STATUSES = [
        (CON_HIEU_LUC, CON_HIEU_LUC),
        (HET_HIEU_LUC_MOT_PHAN, HET_HIEU_LUC_MOT_PHAN),
        (CHUA_CO_HIEU_LUC, CHUA_CO_HIEU_LUC),
        (HET_HIEU_LUC_TOAN_BO, HET_HIEU_LUC_TOAN_BO),
        (NGUNG_HIEU_LUC, NGUNG_HIEU_LUC),
        (CHUA_XAC_DINH, CHUA_XAC_DINH),
        (NGUNG_HIEU_LUC_MOT_PHAN, NGUNG_HIEU_LUC_MOT_PHAN),
        (KHONG_CON_PHU_HOP, KHONG_CON_PHU_HOP),
        (KHONG_XAC_DINH,KHONG_XAC_DINH)
    ]
    LAW_DOCUMENT_TYPES = [
        (QUYET_DINH, QUYET_DINH),
        (THONG_TU, THONG_TU),
        (NGHI_QUYET, NGHI_QUYET),
        (NGHI_DINH, NGHI_DINH),
        (THONG_TU_LIEN_TICH, THONG_TU_LIEN_TICH),
        (LUAT, LUAT),
        (VAN_BAN_HOP_NHAT, VAN_BAN_HOP_NHAT),
        (PHAP_LENH, PHAP_LENH),
        (CONG_VAN, CONG_VAN),
        (BO_LUAT, BO_LUAT),
        (NGHI_QUYET_LIEN_TICH, NGHI_QUYET_LIEN_TICH),
        (CHI_THI, CHI_THI),
        (VAN_BAN_KHAC, VAN_BAN_KHAC),
        (LENH, LENH),
        (HIEN_PHAP, HIEN_PHAP),
        (VAN_BAN_LIEN_QUAN, VAN_BAN_LIEN_QUAN),
        (THONG_BAO, THONG_BAO),
        (CHUONG_TRINH, CHUONG_TRINH),
        (SAC_LENH, SAC_LENH),
        (THONG_TU_LIEN_BO, THONG_TU_LIEN_BO),
        (HIEP_DINH, HIEP_DINH),
        (SAC_LUAT, SAC_LUAT),
        (BAO_CAO, BAO_CAO),
        (CONG_DIEN, CONG_DIEN),
        (DIEU_UOC_QUOC_TE, DIEU_UOC_QUOC_TE),
        (HUONG_DAN, HUONG_DAN),
        (KE_HOACH, KE_HOACH),
        (VAN_BAN_WTO, VAN_BAN_WTO),
        (DU_THAO,  DU_THAO)
    ]

    ID = models.BigIntegerField(blank=True, null=True)
    # task_id = models.UUIDField(default=uuid.uuid4)
    es_id = models.BigIntegerField(blank=True, null=True)
    title = models.TextField(blank=True, null=True)
    dia_danh = models.CharField(max_length=200, blank=True, null=True)
    ngay_ban_hanh = models.CharField(max_length=100, blank=True, null=True)
    ngay_co_hieu_luc = models.DateTimeField(blank=True, null=True)
    ngay_dang_cong_bao = models.DateTimeField(blank=True, null=True)
    ngay_het_hieu_luc = models.DateTimeField(blank=True, null=True)
    ngay_het_hieu_luc_mot_phan = models.DateTimeField(blank=True, null=True)
    so_hieu = models.CharField(max_length=200, blank=True, null=True)
    toan_van = models.TextField(blank=True, null=True)
    trich_yeu = models.TextField(blank=True, null=True)
    tinh_trang_hieu_luc = models.CharField(max_length=100, choices=VALIDITY_STATUSES, blank=True, null=True)
    co_quan_ban_hanh = models.CharField(max_length=500, blank=True, null=True)
    nguoi_ky = models.CharField(max_length=500, blank=True, null=True)
    loai_van_ban = models.CharField(max_length=50, choices=LAW_DOCUMENT_TYPES, blank=True, null=True)
    class Meta:
        abstract = True


class LegalSearchHistory(models.Model):
    id = models.BigAutoField(primary_key=True)

    query = models.TextField()
    result_count = models.IntegerField(default=0)
    response_time = models.FloatField()
    domain_list = ArrayField(models.IntegerField())
    requested_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, default=None)
    created_time = models.DateTimeField(auto_now_add=True)
    deleted_time = models.DateTimeField(null=True)
    rate = models.IntegerField(default=-1)
    comment = models.TextField(null=True)


class WorkSpace(TimeStampedModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    description = models.CharField(max_length=500)
    is_deleted = models.BooleanField(default=False)
    is_no_folder = models.BooleanField(default=False)

class Note(TimeStampedModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(WorkSpace, related_name='notes', on_delete=models.CASCADE)
    document_name = models.CharField(max_length=250,blank=True, null=True)
    clause_detail = models.CharField(max_length=250,blank=True, null=True)
    content = models.TextField(null=True, blank=True)

class Document(TimeStampedModel, MetadataDocument):
    TYPES = [
        (BY_USER, 'BY_USER'),
        (BY_AI, 'BY_AI')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "WorkSpace", on_delete=models.PROTECT, blank=True, null=True
    )
    related_documents = models.ManyToManyField("self", related_name="related_documents", null=True, blank=True)

    types = models.IntegerField(choices=TYPES, default=BY_USER)
    name = models.CharField(max_length=1000, null=True, blank=True)
    origin = models.FileField(upload_to="document/%Y/%m/%d", null=True, blank=True)
    convert = models.FileField(upload_to="document/%Y/%m/%d", null=True, blank=True)
    is_convert = models.BooleanField(default=False)
    convert_percentage = models.IntegerField(default=0)
    convert_datetime = models.CharField(max_length=50, null=True, blank=True)
    convert_session_id = models.UUIDField(null=True, blank=True)
    status = models.SmallIntegerField(choices=const.STATUS, default=const.STATUS_PENDING)
    json_data = models.JSONField(null=True, blank=True)
    convert_json = models.BooleanField(default=False)
    compare_status = models.SmallIntegerField(choices=const.COMPARE_STATUS, default=const.STATUS_INIT)
    authority_status = models.SmallIntegerField(choices=const.AUTHORITY_STATUS, default=const.STATUS_INIT)
    noi_dung_done = models.BooleanField(default=False)
    hinh_thuc_done = models.BooleanField(default=False)
    hieu_luc_done = models.BooleanField(default=False)
    khac_biet_done = models.BooleanField(default=False)
    ie_done = models.BooleanField(default=False)
    percentage = models.IntegerField(default=0)
    # order = models.IntegerField(default=0)
    # order_update_time = models.DateTimeField(null=True, blank=True)

    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    def save(self, *args, **kwargs):
        # Determine the file type
        if self.origin:
            mime_type, _ = mimetypes.guess_type(self.origin.name)
            file_type = mime_type or 'application/octet-stream'

            # Check if the file is a DOCX file
            if file_type not in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                 'application/msword']:
                self.is_convert = True
            else:
                self.is_convert = False

        super().save(*args, **kwargs)

class DocumentRelated(models.Model):
    RELATION_TYPE = [
        (VAN_BAN_BI_BAI_BO, "Văn bản bị bãi bỏ"),
        (VAN_BAN_BI_BAI_BO_1_PHAN, "Văn bản bị bãi bỏ một phần"),
        (VAN_BAN_BI_DINH_CHI, "Văn bản bị đình chỉ"),
        (VAN_BAN_BI_DINH_CHI_1_PHAN, "Văn bản bị đình chỉ một phần"),
        (VAN_BAN_BI_DINH_CHINH, "Văn bản bị đính chính"),
        (VAN_BAN_BI_HUY_BO, "Văn bản bị huỷ bỏ"),
        (VAN_BAN_BI_HUY_BO_1_PHAN, "Văn bản bị huỷ bỏ một phần"),
        (VAN_BAN_BI_THAY_THE, "Văn bản bị thay thế"),
        (VAN_BAN_BI_THAY_THE_1_PHAN, "Văn bản bị thay thế một phần"),
        (VAN_BAN_CAN_CU, "Văn bản căn cứ"),
        (VAN_BAN_CHUA_XAC_DINH, "Văn bản chưa xác định"),
        (VAN_BAN_CHUA_XAC_DINH_1_PHAN, "Văn bản chưa xác định một phần"),
        (VAN_BAN_DAN_CHIEU, "Văn bản dẫn chiếu"),
        (VAN_BAN_DUOC_HUONG_DAN, "Văn bản được hướng dẫn"),
        (VAN_BAN_DUOC_SUA_DOI, "Văn bản được sửa đổi"),
        (VAN_BAN_DUOC_SUA_DOI_BO_SUNG, "Văn bản được sửa đổi, bổ sung"),
        (VAN_BAN_LIEN_QUAN_KHAC, "Văn bản liên quan khác"),
        (VAN_BAN_QUY_DINH_CHI_TIET, "Văn bản quy định chi tiết"),
        (VAN_BAN_BAI_BO , "Văn bản bãi bỏ"),
        (VAN_BAN_BI_HET_HIEU_LUC , "Văn bản bị hết hiệu lực"),
        (VAN_BAN_BI_HET_HIEU_LUC_1_PHAN , "Văn bản bị hết hiệu lực một phần"),
        (VAN_BAN_DINH_CHI,"Văn bản đình chỉ"),
        (VAN_BAN_DINH_CHI_1_PHAN , "Văn bản đình chỉ một phần"),
        (VAN_BAN_DUOC_QUY_DINH_CHI_TIET, "Văn bản được quy định chi tiết"),
        (VAN_BAN_HUONG_DAN ,"Văn bản hướng dẫn"),
        (VAN_BAN_HET_HIEU_LUC,"Văn bản hết hiệu lực"),
        (VAN_BAN_HET_HIEU_LUC_1_PHAN,"Văn bản hết hiệu lực một phần"),
        (VAN_BAN_HUY_BO , "Văn bản hủy bỏ"),
        (VAN_BAN_HUY_BO_1_PHAN,"Văn bản hủy bỏ một phần"),
        (VAN_BAN_SUA_DOI,"Văn bản sửa đổi"),
        (VAN_BAN_SUA_DOI_BO_SUNG,"Văn bản sửa đổi bổ sung"),
        (VAN_BAN_THAY_THE,"Văn bản thay thế"),
        (VAN_BAN_BI_THAY_THE_1_PHAN,"Văn bản thay thế một phần"),
    ]
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    relation_type = models.CharField(choices = RELATION_TYPE, max_length=1000) # ví dụ: "van_ban_can_cu"
    ID = models.BigIntegerField(null=True, blank=True)
    score = models.FloatField(blank=True, null=True)
    url = models.URLField(max_length=1000, blank=True, null=True)
    title = models.CharField(max_length=5000, blank=True, null=True)
    toan_van = models.TextField(blank=True, null=True)
    pham_vi = models.CharField(max_length=1000, null=True, blank=True)
    so_hieu = models.CharField(max_length=100)
    vbpl_id = models.CharField(max_length=100, null=True, blank=True)
    nguoi_ky = models.CharField(max_length=255, null=True, blank=True)
    don_vi = ArrayField(models.CharField(max_length=255), null=True, blank=True)
    so_hieu = models.CharField(max_length=255, null=True, blank=True)
    ten_van_ban = models.CharField(max_length=1000)
    trich_yeu = models.TextField(null=True, blank=True)
    loai_van_ban = models.CharField(max_length=100)
    ngay_ap_dung = models.DateTimeField(null=True, blank=True)
    ngay_ban_hanh = models.DateTimeField(null=True, blank=True)
    co_quan_ban_hanh = models.CharField(max_length=255, blank=True, null=True)
    ngay_co_hieu_luc = models.DateTimeField(null=True, blank=True) 
    tinh_trang_hieu_luc = models.CharField(max_length=100, null=True, blank=True)
    noi_dung = models.TextField(null=True, blank=True)

class DocumentCompare(TimeStampedModel):
    id = models.BigAutoField(primary_key=True)

    document_based = models.JSONField(null=True, blank=True)
    document_related = models.JSONField(null=True, blank=True)
    dieu_khoan = models.JSONField(null=True, blank=True)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return f"Document{self.document_id}_{self.id}"

class DocumentAuthority(TimeStampedModel):
    id = models.BigAutoField(primary_key=True)

    noi_dung = models.JSONField(null=True, blank=True)
    hinh_thuc = models.JSONField(null=True, blank=True)
    hieu_luc = models.JSONField(null=True, blank=True)
    khac_biet = models.JSONField(null=True, blank=True)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return f"Document{self.document_id}_{self.id}"

class Package(models.Model):
    id = models.CharField(primary_key=True, max_length=20)
    upload_type = models.CharField(max_length=50, default='docx,pdf',blank=True, null=True)
    download_type = models.CharField(max_length=50, default='docx,pdf',blank=True, null=True)
    max_file_size_per_upload = models.IntegerField(default=5000, null=True, blank=True)
    max_num_doc_per_upload = models.IntegerField(default=5, null=True, blank=True)
    limit_request = models.IntegerField(default=50, null=True, blank=True)
    description = models.CharField(max_length=1000, null=True, blank=True)

    def __str__(self) -> str:
        return self.id
    
class Quota(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        primary_key=True,
    )
    package = models.ForeignKey(
        Package, on_delete=models.PROTECT, null=True, blank=True, related_name="user_quota"
    )
    roles = models.ForeignKey(
        "Role", on_delete=models.PROTECT, null=True, blank=True, related_name="user_role"
    )
    created_time = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f'{self.user.email}-{self.package.id}'

class HistorySearchResult(models.Model) :
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
    )
    data = models.TextField()  
    params = models.TextField()  
    search_time = models.DateTimeField(auto_now_add=True)  
    type = models.CharField(max_length=50, default=const.AI_SEARCH,blank=True, null=False)
    class Meta:
        ordering = ['-search_time']  

class LawClause(TimeStampedModel):
    STATUSES = [
        (STATUS_INIT, 'Chưa rà soát'),
        (STATUS_SUCCESS, 'Thành công'),
        (STATUS_PENDING, 'Chờ xử lý'),
        (STATUS_PROCESSING, 'Đang xử lý'),
        (STATUS_FAILED, 'Thất bại')
    ]
    CLAUSE_TYPES = [
        (THAM_QUYEN_NOI_DUNG, 'Thẩm quyền nội dung'),
        (DIEU_KHOAN_LIEN_QUAN, 'Điều khoản liên quan')
    ]
    document = models.ForeignKey(Document, on_delete=models.CASCADE, null=True, blank=True)
    related_clauses = models.ManyToManyField("self", related_name="related_clauses", null=True, blank=True)
    
    status = models.SmallIntegerField(choices=STATUSES, null=True, blank=True)
    type = models.CharField(choices=CLAUSE_TYPES, default=const.STATUS_INIT)
    clause_id = models.CharField(max_length=100, null=True, blank=True) # ID phía AI
    law_title = models.TextField(null=True, blank=True)
    title = models.TextField(null=True, blank=True)
    reason = models.TextField(null=True, blank=True)
    position = models.CharField(max_length=200, null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    show_content = models.TextField(null=True, blank=True)
    doc_id = models.CharField(max_length=100, null=True, blank=True) # ID phía AI
    is_raw = models.BooleanField(null=True, blank=True) # ID phía AI
    result = models.CharField(max_length=100, null=True, blank=True)
    ai_result = models.JSONField(null=True, blank=True)
    
    different_status = models.BooleanField(null=True, blank=True)
    
    ly_do = models.TextField(null=True, blank=True)
    ket_luan = models.TextField(null=True, blank=True)
    giai_phap = models.TextField(null=True, blank=True)
    order = models.IntegerField(default=0,null=True, blank=True)

class ThamQuyenNoiDung(TimeStampedModel):
    lawclause = models.ForeignKey(LawClause, on_delete=models.CASCADE)
    related_clauses = models.ManyToManyField(LawClause, related_name="tqnd_related_clauses", null=True, blank=True)
    van_de_ban_hanh = models.TextField(null=True, blank=True)
    co_quan_co_tham_quyen = models.TextField(null=True, blank=True)
    ket_qua = models.TextField(null=True, blank=True)
    ly_do = models.TextField(null=True, blank=True)
    
class Role(TimeStampedModel):
    name = models.CharField(max_length=100)
    description = models.CharField(max_length=500)
    workspaces = models.ManyToManyField(WorkSpace, related_name="role_workspaces", null=True, blank=True)

class Report(TimeStampedModel):
    REPORT_TYPES = [
        ('bug', 'Bug Report'),
        ('ui', 'UI Report')
    ]
    
    STATUS_CHOICES = [
        (0, 'Pending'),
        (1, 'Resolved')
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    report_type = models.CharField(max_length=10, choices=REPORT_TYPES)
    status = models.IntegerField(choices=STATUS_CHOICES, default=0)
    attachments = models.FileField(upload_to='reports/%Y/%m/%d', null=True, blank=True)
    
    def save(self, *args, **kwargs):
        if self.attachments:
            # Check if the file is an image
            mime_type, _ = mimetypes.guess_type(self.attachments.name)
            if not mime_type or not mime_type.startswith('image/'):
                raise ValueError("Only image files are allowed as attachments")
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"Report by {self.user.email} - {self.report_type}"

class DocumentSummarize(TimeStampedModel):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, null=True, blank=True)
    summarize = models.TextField(null=True, blank=True)
    status = models.SmallIntegerField(choices=const.STATUS_SUMMARIZE, default=const.STATUS_FAILED)
    
class DocumentCompareResult(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document_old = models.ForeignKey(Document, on_delete=models.CASCADE,related_name='compare_results_as_old', null=True, blank=True)
    document_new = models.ForeignKey(Document, on_delete=models.CASCADE,related_name='compare_results_as_new', null=True, blank=True)
    old_content = models.JSONField(null=True, blank=True)
    new_content = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)